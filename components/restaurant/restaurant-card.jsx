import Link from "next/link";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "../ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, MapPin, Star, ChefHat } from "lucide-react";
import { FavoriteButton } from "@/components/FavoriteButton";
import { HalalVerificationButton } from "@/components/HalalVerificationButton";
import { SocialShareButton } from "@/components/SocialShare";
import { siteConfig } from "@/lib/seo";

const RestaurantCard = ({
  restaurant,
  showFavoriteButton = true,
  isFavorited = false,
  isHalalVerified = false,
  halalCount = 0,
  showHalalVerification = true,
}) => {
  return (
    <div className="relative">
      <Link href={`/restaurants/${restaurant.slug}`}>
        <Card className="hover:shadow-md transition-all duration-200 group hover:border-primary/20">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <CardTitle className="group-hover:text-primary transition-colors capitalize">
                  {restaurant.name}
                </CardTitle>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    <ChefHat className="w-3 h-3" />
                    {restaurant.type || "Restaurant"}
                  </Badge>
                  {restaurant.isFeatured && (
                    <Badge
                      variant="default"
                      className="flex items-center gap-1"
                    >
                      <Star className="w-3 h-3" />
                      Featured
                    </Badge>
                  )}
                  {restaurant.cuisines && restaurant.cuisines.length > 0 && (
                    <Badge variant="outline">
                      {restaurant.cuisines[0]}
                      {restaurant.cuisines.length > 1 &&
                        ` +${restaurant.cuisines.length - 1}`}
                    </Badge>
                  )}
                </div>
              </div>
              <ChevronRight className="w-5 h-5 text-muted-foreground group-hover:text-primary transition-colors" />
            </div>
          </CardHeader>
          <CardContent className="pb-16">
            <div className="space-y-3 text-muted-foreground">
              <div className="flex items-center text-sm">
                <MapPin className="w-4 h-4 mr-2" />
                <div>
                  <span className="capitalize">{restaurant.city}</span>,
                  <span className="uppercase ml-0.5">{restaurant.state}</span>
                </div>
              </div>

              {restaurant.highlights && restaurant.highlights.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {restaurant.highlights.slice(0, 3).map((highlight) => (
                    <Badge
                      key={highlight}
                      variant="outline"
                      className="text-xs capitalize"
                    >
                      {highlight}
                    </Badge>
                  ))}
                  {restaurant.highlights.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{restaurant.highlights.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </Link>

      {/* Action Buttons - Outside Link to prevent redirect */}
      <div className="absolute bottom-0 left-0 right-0 p-4 pt-0 flex gap-2 justify-center items-center bg-white">
        {/* Favorite Button */}
        {showFavoriteButton && (
          <FavoriteButton
            restaurantId={restaurant.id}
            initialIsFavorited={isFavorited}
            favoritesCount={restaurant._count?.favorites || 0}
          />
        )}

        {/* Halal Verification Button */}
        {showHalalVerification && (
          <HalalVerificationButton
            restaurantId={restaurant.id}
            initialIsVerified={isHalalVerified}
            halalCount={halalCount}
          />
        )}

        {/* Social Share Button */}
        {/* <SocialShareButton
          url={`${siteConfig.url}/restaurants/${restaurant.slug}`}
          title={`${restaurant.name} - Halal Restaurant in ${restaurant.city}, ${restaurant.state}`}
          description={`Check out ${restaurant.name}, a verified halal restaurant in ${restaurant.city}, ${restaurant.state}. Find directions and more!`}
          hashtags={["halal", "restaurant", restaurant.city?.toLowerCase()]}
        /> */}
      </div>
    </div>
  );
};

export default RestaurantCard;
