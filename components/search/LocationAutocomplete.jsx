"use client";

import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { MapPin, Loader2, X } from "lucide-react";
import { useLocationAutocomplete, getLocationIcon } from "@/hooks/useLocationAutocomplete";
import { cn } from "@/lib/utils";

export function LocationAutocomplete({
  value,
  onChange,
  placeholder = "City, State or ZIP",
  className = "",
  inputClassName = "",
  onLocationSelect,
  ...props
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value || "");
  const inputRef = useRef(null);
  const dropdownRef = useRef(null);

  const {
    suggestions,
    isLoading,
    error,
    searchLocations,
    clearSuggestions,
  } = useLocationAutocomplete();

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    onChange?.(newValue);

    if (newValue.length >= 3) {
      searchLocations(newValue);
      setIsOpen(true);
    } else {
      clearSuggestions();
      setIsOpen(false);
    }
  };

  // Handle suggestion selection
  const handleSuggestionSelect = (suggestion) => {
    setInputValue(suggestion.shortName);
    onChange?.(suggestion.shortName);
    onLocationSelect?.(suggestion);
    setIsOpen(false);
    clearSuggestions();
    inputRef.current?.blur();
  };

  // Handle input focus
  const handleFocus = () => {
    if (suggestions.length > 0) {
      setIsOpen(true);
    }
  };

  // Handle input blur (with delay to allow clicking suggestions)
  const handleBlur = () => {
    setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  // Handle clear button
  const handleClear = () => {
    setInputValue("");
    onChange?.("");
    clearSuggestions();
    setIsOpen(false);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (e.key === "Escape") {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        !inputRef.current?.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update input value when prop changes
  useEffect(() => {
    setInputValue(value || "");
  }, [value]);

  return (
    <div className={cn("relative", className)}>
      {/* Input Field */}
      <div className="relative group">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <MapPin className="h-5 w-5 text-gray-400 group-focus-within:text-primary transition-colors" />
        </div>
        
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={cn(
            "pl-12 pr-10 border-gray-300 focus:border-primary focus:ring-primary rounded-xl transition-all duration-200 bg-gray-50 focus:bg-white hover:bg-white",
            inputClassName
          )}
          autoComplete="off"
          {...props}
        />

        {/* Clear Button */}
        {inputValue && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {isOpen && (suggestions.length > 0 || error) && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-auto"
        >
          {error && (
            <div className="px-4 py-3 text-sm text-red-600 border-b border-gray-100">
              {error}
            </div>
          )}

          {suggestions.map((suggestion) => (
            <button
              key={suggestion.id}
              type="button"
              onClick={() => handleSuggestionSelect(suggestion)}
              className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none transition-colors border-b border-gray-50 last:border-b-0"
            >
              <div className="flex items-center gap-3">
                <span className="text-lg" role="img" aria-label={suggestion.type}>
                  {getLocationIcon(suggestion.type)}
                </span>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 truncate">
                    {suggestion.displayName}
                  </div>
                  {suggestion.fullAddress !== suggestion.displayName && (
                    <div className="text-sm text-gray-500 truncate">
                      {suggestion.fullAddress}
                    </div>
                  )}
                </div>
                <div className="text-xs text-gray-400 capitalize">
                  {suggestion.type}
                </div>
              </div>
            </button>
          ))}

          {suggestions.length === 0 && !error && !isLoading && (
            <div className="px-4 py-3 text-sm text-gray-500">
              No locations found. Try a different search term.
            </div>
          )}
        </div>
      )}
    </div>
  );
}
