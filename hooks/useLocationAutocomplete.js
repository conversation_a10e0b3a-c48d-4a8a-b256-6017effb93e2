"use client";

import { useState, useEffect, useCallback } from "react";
import { debounce } from "lodash";

// Custom hook for location autocomplete
export function useLocationAutocomplete() {
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Debounced search function to avoid too many API calls
  const debouncedSearch = useCallback(
    debounce(async (query) => {
      if (!query || query.length < 3) {
        setSuggestions([]);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Use Nominatim (OpenStreetMap) geocoding service for autocomplete
        const encodedQuery = encodeURIComponent(query);
        const response = await fetch(
          `https://nominatim.openstreetmap.org/search?format=json&q=${encodedQuery}&limit=8&countrycodes=us&addressdetails=1&extratags=1`
        );

        if (!response.ok) {
          throw new Error("Failed to fetch location suggestions");
        }

        const data = await response.json();

        // Process and format the suggestions
        const formattedSuggestions = data
          .map((item) => {
            const address = item.address || {};
            
            // Create a formatted display name
            let displayName = "";
            let shortName = "";
            
            // Handle different types of locations
            if (address.postcode) {
              // ZIP code
              shortName = address.postcode;
              displayName = `${address.postcode}`;
              if (address.city || address.town || address.village) {
                displayName += ` - ${address.city || address.town || address.village}`;
              }
              if (address.state) {
                displayName += `, ${address.state}`;
              }
            } else if (address.city || address.town || address.village) {
              // City
              const cityName = address.city || address.town || address.village;
              shortName = cityName;
              displayName = cityName;
              if (address.state) {
                displayName += `, ${address.state}`;
              }
            } else if (address.county) {
              // County
              shortName = address.county;
              displayName = `${address.county} County`;
              if (address.state) {
                displayName += `, ${address.state}`;
              }
            } else if (address.state) {
              // State
              shortName = address.state;
              displayName = address.state;
            } else {
              // Fallback to display_name
              displayName = item.display_name;
              shortName = displayName.split(",")[0];
            }

            return {
              id: item.place_id,
              displayName: displayName,
              shortName: shortName,
              fullAddress: item.display_name,
              lat: parseFloat(item.lat),
              lng: parseFloat(item.lon),
              type: getLocationType(address),
              address: address,
            };
          })
          // Remove duplicates based on display name
          .filter((item, index, self) => 
            index === self.findIndex(t => t.displayName === item.displayName)
          )
          // Sort by relevance (ZIP codes first, then cities, then others)
          .sort((a, b) => {
            const typeOrder = { zipcode: 0, city: 1, county: 2, state: 3, other: 4 };
            return typeOrder[a.type] - typeOrder[b.type];
          });

        setSuggestions(formattedSuggestions);
      } catch (err) {
        console.error("Location autocomplete error:", err);
        setError("Failed to load location suggestions");
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 300), // 300ms debounce
    []
  );

  // Function to search for locations
  const searchLocations = useCallback((query) => {
    if (!query || query.length < 3) {
      setSuggestions([]);
      setIsLoading(false);
      return;
    }
    
    setIsLoading(true);
    debouncedSearch(query);
  }, [debouncedSearch]);

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  return {
    suggestions,
    isLoading,
    error,
    searchLocations,
    clearSuggestions,
  };
}

// Helper function to determine location type
function getLocationType(address) {
  if (address.postcode) return "zipcode";
  if (address.city || address.town || address.village) return "city";
  if (address.county) return "county";
  if (address.state) return "state";
  return "other";
}

// Helper function to get location icon based on type
export function getLocationIcon(type) {
  switch (type) {
    case "zipcode":
      return "📮";
    case "city":
      return "🏙️";
    case "county":
      return "🏞️";
    case "state":
      return "🗺️";
    default:
      return "📍";
  }
}
