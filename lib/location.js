// Location utilities for geolocation and distance calculations

// Calculate distance between two points using Haversine formula
export function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 3959; // Earth's radius in miles
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c; // Distance in miles

  return distance;
}

function toRadians(degrees) {
  return degrees * (Math.PI / 180);
}

// Get user's current location
export function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error("Geolocation is not supported by this browser"));
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
        });
      },
      (error) => {
        let errorMessage = "Unable to retrieve your location";

        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access denied by user";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out";
            break;
        }

        reject(new Error(errorMessage));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  });
}

// Filter restaurants by distance
export function filterRestaurantsByDistance(
  restaurants,
  userLat,
  userLng,
  radiusMiles
) {
  return restaurants
    .map((restaurant) => {
      if (!restaurant.lat || !restaurant.lng) {
        return { ...restaurant, distance: null };
      }

      const distance = calculateDistance(
        userLat,
        userLng,
        restaurant.lat,
        restaurant.lng
      );
      return { ...restaurant, distance };
    })
    .filter(
      (restaurant) =>
        restaurant.distance !== null && restaurant.distance <= radiusMiles
    )
    .sort((a, b) => a.distance - b.distance);
}

// Format distance for display
export function formatDistance(distance) {
  if (distance === null || distance === undefined) {
    return "Distance unknown";
  }

  if (distance < 0.1) {
    return "Less than 0.1 mi";
  }

  return `${distance.toFixed(1)} mi`;
}

// Geocode address to get coordinates (using a free geocoding service)
export async function geocodeAddress(address) {
  try {
    // Using Nominatim (OpenStreetMap) free geocoding service
    const encodedAddress = encodeURIComponent(address);
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=1&countrycodes=us`
    );

    if (!response.ok) {
      throw new Error("Geocoding service unavailable");
    }

    const data = await response.json();

    if (data.length === 0) {
      throw new Error("Address not found");
    }

    return {
      latitude: parseFloat(data[0].lat),
      longitude: parseFloat(data[0].lon),
      displayName: data[0].display_name,
    };
  } catch (error) {
    console.error("Geocoding error:", error);
    throw new Error("Unable to geocode address");
  }
}

// Default radius options for nearby search
export const RADIUS_OPTIONS = [
  { value: 5, label: "5 miles" },
  { value: 10, label: "10 miles" },
  { value: 25, label: "25 miles" },
  { value: 50, label: "50 miles" },
];

// Check if browser supports geolocation
export function isGeolocationSupported() {
  return typeof navigator !== "undefined" && "geolocation" in navigator;
}
