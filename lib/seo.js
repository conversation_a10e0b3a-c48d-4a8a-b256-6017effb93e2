// SEO Configuration and Utilities
export const siteConfig = {
  name: "HalalBuzz - Halal Restaurant Directory",
  description:
    "Discover authentic halal restaurants in your area. Find verified halal dining options, read reviews, and explore the best halal cuisine near you.",
  url: process.env.NEXT_PUBLIC_SITE_URL || "https://halalbuzz.com",
  ogImage: "/og-image.jpg",
  twitterHandle: "@halalbuzz",
  keywords: [
    "halal restaurants",
    "halal food",
    "halal dining",
    "muslim restaurants",
    "halal cuisine",
    "halal directory",
    "verified halal",
    "halal near me",
    "islamic food",
    "halal certified",
  ],
};

// Generate structured data for restaurants
export function generateRestaurantStructuredData(restaurant) {
  return {
    "@context": "https://schema.org",
    "@type": "Restaurant",
    name: restaurant.name,
    description:
      restaurant.description ||
      `Halal restaurant serving ${restaurant.cuisine || "delicious food"} in ${
        restaurant.city
      }, ${restaurant.state}`,
    url: `${siteConfig.url}/restaurants/${restaurant.slug}`,
    image:
      restaurant.imageUrl || `${siteConfig.url}/placeholder-restaurant.jpg`,
    address: {
      "@type": "PostalAddress",
      streetAddress: restaurant.address,
      addressLocality: restaurant.city,
      addressRegion: restaurant.state,
      postalCode: restaurant.zipCode,
      addressCountry: "US",
    },
    telephone: restaurant.phone,
    servesCuisine: restaurant.cuisine,
    priceRange: restaurant.priceRange || "$$",
    acceptsReservations: true,
    hasMenu: restaurant.menuUrl ? restaurant.menuUrl : undefined,
    aggregateRating: restaurant.rating
      ? {
          "@type": "AggregateRating",
          ratingValue: restaurant.rating,
          reviewCount: restaurant.reviewCount || 1,
        }
      : undefined,
    additionalProperty: [
      {
        "@type": "PropertyValue",
        name: "Halal Certified",
        value: "Yes",
      },
      {
        "@type": "PropertyValue",
        name: "Dietary Restrictions",
        value: "Halal",
      },
    ],
  };
}

// Generate breadcrumb structured data
export function generateBreadcrumbStructuredData(items) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: `${siteConfig.url}${item.url}`,
    })),
  };
}

// Generate local business structured data
export function generateLocalBusinessStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    logo: `${siteConfig.url}/logo.png`,
    sameAs: [
      "https://twitter.com/halalbuzz",
      "https://facebook.com/halalbuzz",
      "https://instagram.com/halalbuzz",
    ],
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "Customer Service",
      email: "<EMAIL>",
    },
  };
}

// Generate FAQ structured data
export function generateFAQStructuredData(faqs) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };
}

// SEO-friendly URL generation
export function generateSEOSlug(text) {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single
    .trim("-"); // Remove leading/trailing hyphens
}

// Meta tags generator
export function generateMetaTags({
  title,
  description,
  keywords = [],
  canonical,
  ogImage,
  noindex = false,
  type = "website",
}) {
  const fullTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;
  const metaDescription = description || siteConfig.description;
  const allKeywords = [...siteConfig.keywords, ...keywords].join(", ");

  return {
    title: fullTitle,
    description: metaDescription,
    keywords: allKeywords,
    robots: noindex ? "noindex,nofollow" : "index,follow",
    canonical: canonical || siteConfig.url,
    openGraph: {
      title: fullTitle,
      description: metaDescription,
      url: canonical || siteConfig.url,
      siteName: siteConfig.name,
      images: [
        {
          url: ogImage || siteConfig.ogImage,
          width: 1200,
          height: 630,
          alt: fullTitle,
        },
      ],
      locale: "en_US",
      type: type,
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description: metaDescription,
      images: [ogImage || siteConfig.ogImage],
      creator: siteConfig.twitterHandle,
    },
  };
}
