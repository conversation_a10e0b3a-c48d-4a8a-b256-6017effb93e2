const TermsPage = () => {
  return (
    <div className="max-w-3xl mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold mb-6">Terms of Service</h1>
      <p className="mb-4">
        Effective Date: <strong>06/22/2025</strong>
      </p>

      <section className="space-y-4">
        <h2 className="text-2xl font-semibold">1. Acceptance of Terms</h2>
        <p>
          By accessing or using our website, you agree to be bound by these
          Terms and our Privacy Policy.
        </p>

        <h2 className="text-2xl font-semibold">2. Services Provided</h2>
        <p>
          We provide a directory listing of halal food restaurants. Registered
          users can like and search listings.
        </p>

        <h2 className="text-2xl font-semibold">3. User Accounts</h2>
        <p>
          You may need to create an account with your email. You're responsible
          for your account security.
        </p>

        <h2 className="text-2xl font-semibold">4. Acceptable Use</h2>
        <ul className="list-disc pl-6">
          <li>Do not use the site for unlawful purposes.</li>
          <li>Do not interfere with the site's operation.</li>
          <li>No scraping or unauthorized data use.</li>
        </ul>

        <h2 className="text-2xl font-semibold">5. Content Ownership</h2>
        <p>
          We own the site's content except for third-party restaurant names and
          logos.
        </p>

        <h2 className="text-2xl font-semibold">6. User Content</h2>
        <p>
          By submitting content, you give us permission to use it to improve our
          service.
        </p>

        <h2 className="text-2xl font-semibold">7. Termination</h2>
        <p>We may suspend or terminate access for violating these Terms.</p>

        <h2 className="text-2xl font-semibold">8. Disclaimers</h2>
        <p>
          We do not guarantee restaurant halal certification. Please verify
          independently.
        </p>

        <h2 className="text-2xl font-semibold">9. Limitation of Liability</h2>
        <p>
          We are not liable for any indirect or incidental damages from using
          the site.
        </p>

        <h2 className="text-2xl font-semibold">10. Changes to Terms</h2>
        <p>
          We may update these Terms. Continued use indicates your agreement.
        </p>
      </section>

      <hr className="my-12" />
    </div>
  );
};

export default TermsPage;
