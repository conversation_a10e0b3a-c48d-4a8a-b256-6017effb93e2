import prisma from "@/lib/prisma";
import { siteConfig } from "@/lib/seo";

export async function GET() {
  try {
    // Get all restaurants for sitemap
    const restaurants = await prisma.restaurant.findMany({
      select: {
        slug: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    // Static pages
    const staticPages = [
      {
        url: "",
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 1.0,
      },
      {
        url: "/restaurants",
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 0.9,
      },
      {
        url: "/search",
        lastModified: new Date(),
        changeFrequency: "weekly",
        priority: 0.8,
      },
    ];

    // Restaurant pages
    const restaurantPages = restaurants.map((restaurant) => ({
      url: `/restaurants/${restaurant.slug}`,
      lastModified: restaurant.updatedAt,
      changeFrequency: "weekly",
      priority: 0.7,
    }));

    const allPages = [...staticPages, ...restaurantPages];

    // Generate XML sitemap
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages
  .map(
    (page) => `
  <url>
    <loc>${siteConfig.url}${page.url}</loc>
    <lastmod>${page.lastModified.toISOString()}</lastmod>
    <changefreq>${page.changeFrequency}</changefreq>
    <priority>${page.priority}</priority>
  </url>`
  )
  .join("")}
</urlset>`;

    return new Response(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating sitemap:", error);
    return new Response("Error generating sitemap", { status: 500 });
  }
}
