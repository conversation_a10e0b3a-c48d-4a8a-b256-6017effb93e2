import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { calculateDistance } from "@/lib/location";

export async function POST(request) {
  try {
    const { latitude, longitude, radius = 10, limit = 50 } = await request.json();

    // Validate required parameters
    if (!latitude || !longitude) {
      return NextResponse.json(
        { error: "Latitude and longitude are required" },
        { status: 400 }
      );
    }

    // Validate coordinates
    if (latitude < -90 || latitude > 90 || longitude < -180 || longitude > 180) {
      return NextResponse.json(
        { error: "Invalid coordinates" },
        { status: 400 }
      );
    }

    // Validate radius
    if (radius <= 0 || radius > 100) {
      return NextResponse.json(
        { error: "Radius must be between 1 and 100 miles" },
        { status: 400 }
      );
    }

    // Get all restaurants with coordinates
    const restaurants = await prisma.restaurant.findMany({
      where: {
        isPublished: true,
        lat: { not: null },
        lng: { not: null },
      },
      include: {
        owner: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
          },
        },
        _count: {
          select: {
            favorites: true,
            halalVerifications: true,
          },
        },
      },
      take: limit * 2, // Get more than needed to account for filtering
    });

    // Calculate distances and filter by radius
    const nearbyRestaurants = restaurants
      .map(restaurant => {
        const distance = calculateDistance(
          latitude,
          longitude,
          restaurant.lat,
          restaurant.lng
        );
        
        return {
          ...restaurant,
          distance: Math.round(distance * 10) / 10, // Round to 1 decimal place
        };
      })
      .filter(restaurant => restaurant.distance <= radius)
      .sort((a, b) => a.distance - b.distance)
      .slice(0, limit);

    return NextResponse.json({
      restaurants: nearbyRestaurants,
      total: nearbyRestaurants.length,
      searchLocation: {
        latitude,
        longitude,
        radius,
      },
    });

  } catch (error) {
    console.error("Error finding nearby restaurants:", error);
    return NextResponse.json(
      { error: "Failed to find nearby restaurants" },
      { status: 500 }
    );
  }
}

// GET endpoint for testing (optional)
export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const lat = parseFloat(searchParams.get('lat'));
  const lng = parseFloat(searchParams.get('lng'));
  const radius = parseInt(searchParams.get('radius')) || 10;

  if (!lat || !lng) {
    return NextResponse.json(
      { error: "lat and lng query parameters are required" },
      { status: 400 }
    );
  }

  // Reuse POST logic
  return POST(new Request(request.url, {
    method: 'POST',
    body: JSON.stringify({ latitude: lat, longitude: lng, radius }),
    headers: { 'Content-Type': 'application/json' }
  }));
}
