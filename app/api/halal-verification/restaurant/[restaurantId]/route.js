import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

// DELETE /api/halal-verification/restaurant/[restaurantId] - Remove halal verification by restaurant ID
export async function DELETE(request, { params }) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { restaurantId } = await params;

    // Find and delete the verification
    const verification = await prisma.halalVerification.findUnique({
      where: {
        userId_restaurantId: {
          userId: user.id,
          restaurantId: restaurantId,
        },
      },
    });

    if (!verification) {
      return NextResponse.json(
        { error: "Halal verification not found" },
        { status: 404 }
      );
    }

    await prisma.halalVerification.delete({
      where: { id: verification.id },
    });

    return NextResponse.json({
      message: "Halal verification removed successfully",
    });
  } catch (error) {
    console.error("Error removing halal verification:", error);
    return NextResponse.json(
      { error: "Failed to remove halal verification" },
      { status: 500 }
    );
  }
}

// GET /api/halal-verification/restaurant/[restaurantId] - Check if user has verified this restaurant as halal
export async function GET(request, { params }) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ hasVerified: false });
    }

    const { restaurantId } = await params;

    const verification = await prisma.halalVerification.findUnique({
      where: {
        userId_restaurantId: {
          userId: user.id,
          restaurantId: restaurantId,
        },
      },
    });

    return NextResponse.json({
      hasVerified: !!verification,
    });
  } catch (error) {
    console.error("Error checking halal verification status:", error);
    return NextResponse.json({ hasVerified: false });
  }
}
