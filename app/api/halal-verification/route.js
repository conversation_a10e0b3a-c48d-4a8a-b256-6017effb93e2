import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { getCurrentUser } from "@/lib/auth";

// GET /api/halal-verification - Get user's halal verifications
export async function GET() {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const verifications = await prisma.halalVerification.findMany({
      where: { userId: user.id },
      include: {
        restaurant: true,
      },
      orderBy: { createdAt: "desc" },
    });

    return NextResponse.json(verifications);
  } catch (error) {
    console.error("Error fetching halal verifications:", error);
    return NextResponse.json(
      { error: "Failed to fetch halal verifications" },
      { status: 500 }
    );
  }
}

// POST /api/halal-verification - Add halal verification
export async function POST(request) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { restaurantId } = await request.json();

    if (!restaurantId) {
      return NextResponse.json(
        {
          error: "Restaurant ID is required",
        },
        { status: 400 }
      );
    }

    // Check if restaurant exists
    const restaurant = await prisma.restaurant.findUnique({
      where: { id: restaurantId },
    });

    if (!restaurant) {
      return NextResponse.json(
        { error: "Restaurant not found" },
        { status: 404 }
      );
    }

    // Check if user already has a verification for this restaurant
    const existingVerification = await prisma.halalVerification.findUnique({
      where: {
        userId_restaurantId: {
          userId: user.id,
          restaurantId: restaurantId,
        },
      },
    });

    if (existingVerification) {
      return NextResponse.json(
        {
          error: "You have already verified this restaurant as halal",
        },
        { status: 400 }
      );
    }

    // Create new verification
    const verification = await prisma.halalVerification.create({
      data: {
        userId: user.id,
        restaurantId: restaurantId,
      },
      include: {
        restaurant: true,
      },
    });

    return NextResponse.json(verification, { status: 201 });
  } catch (error) {
    console.error("Error adding halal verification:", error);
    return NextResponse.json(
      { error: "Failed to add halal verification" },
      { status: 500 }
    );
  }
}
