import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/Navbar";
import { Footer } from "@/components/Footer";
import { ClerkProvider } from "@clerk/nextjs";
import { Toaster } from "sonner";
import {
  siteConfig,
  generateMetaTags,
  generateLocalBusinessStructuredData,
} from "@/lib/seo";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = generateMetaTags({
  title: "Discover Authentic Halal Restaurants Near You",
  description:
    "Find verified halal restaurants, read reviews, and explore the best halal cuisine in your area. Your trusted directory for halal dining experiences.",
  keywords: [
    "halal restaurants near me",
    "halal food directory",
    "muslim restaurants",
    "halal dining guide",
  ],
  canonical: siteConfig.url,
});

export default function RootLayout({ children }) {
  const structuredData = generateLocalBusinessStructuredData();

  return (
    <ClerkProvider>
      <html lang="en">
        <head>
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify(structuredData),
            }}
          />
          <link rel="canonical" href={siteConfig.url} />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <meta name="theme-color" content="#10b981" />
          <link rel="icon" href="/favicon.ico" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <link rel="manifest" href="/manifest.json" />
          <meta
            name="google-site-verification"
            content="ELMMb3EhAA7YGckRDIW4Xm1NO44OAFJEeQD93znxhO8"
          />
        </head>
        <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        >
          <Navbar />
          <main className="min-h-screen">{children}</main>
          <Footer />
          <Toaster position="top-right" />
        </body>
      </html>
    </ClerkProvider>
  );
}
