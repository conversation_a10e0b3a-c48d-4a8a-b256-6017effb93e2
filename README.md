This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

```
# Seed the database
npm run db:seed

# Reset database and re-seed
npm run db:reset

# Or use Prisma directly
npx prisma db seed
```

Set Admin Script

```
npm run set-admin <EMAIL>
```

Database seeding

```
npm run db:seed
npm run db:reset
```

🚀 Testing Instructions
Test Edit Functionality:
Sign in and add a restaurant
Visit the restaurant details page
Click "Edit Restaurant" button
Modify details and save
Test Delete Functionality:
Set yourself as admin: npm run set-admin <EMAIL>
Go to any restaurant edit page
Click "Delete" button (admin only)
Test Pagination:
Visit /restaurants to see paginated list
Use search with many results
Navigate between pages
Test 404 Page:
Visit any non-existent URL
Try the action buttons
Test Footer:
Scroll to bottom of any page
Click footer links

## SEO stuff

As an SEO expert, I'll implement comprehensive SEO best practices for your halal restaurant directory. Let me start by creating the essential SEO infrastructure and then update key pages.

First, let me create a comprehensive SEO configuration and metadata system:

Now let me create a robots.txt file for proper crawling:

Now let me create a dynamic sitemap generator:

Now let me update the main layout to include essential SEO elements:

Let me check the current layout file structure:

Now let me update the layout with proper SEO:

Now let me add structured data to the layout:

Now let me update the main page with better SEO:

Let me check the current page structure:

Let me add SEO metadata to the page:

Now let me update the hero section with better SEO-focused content:

Now let me update the restaurants page with better SEO:

Let me check the current structure:

Let me add SEO metadata to the restaurants page:

Now let me update the restaurant detail page with comprehensive SEO:

Let me check the current structure of the restaurant detail page:

Let me add SEO metadata and structured data to the restaurant detail page:

Now let me add the generateMetadata function for dynamic SEO:

Now let me add structured data to the restaurant page:

Now let me create a web manifest file for PWA features:

Now let me create an SEO-optimized 404 page:

Let me create the 404 page:

Now let me check the diagnostics to ensure everything is working:

Let me clean up the unused imports:

Now let me check the server status:

Let me restart the server:

2. Structured Data (Schema.org)
   Restaurant Schema - Rich snippets for each restaurant
   LocalBusiness Schema - Site-wide business information
   Breadcrumb Schema - Navigation structure
   FAQ Schema - Ready for FAQ sections

3. PWA Manifest
   App-like experience on mobile devices
   Theme colors and branding
   Offline capability preparation
   Install prompts for mobile users
4. Performance Optimizations
   Next.js Image optimization for all restaurant images
   Lazy loading for better Core Web Vitals
   Proper caching headers for static assets
   Optimized font loading with Geist fonts

Features:

H1 optimization - "Find Authentic Halal Restaurants Near You"
Semantic HTML structure with proper heading hierarchy
Local business structured data
Rich content with restaurant stats and features 2. Restaurant Directory Page
Optimized for "halal restaurant directory" keywords
Pagination SEO - Proper rel="next/prev" handling
Category-focused content
Breadcrumb navigation

🔍 Search Engine Optimization Features

1. Keyword Strategy
   Primary Keywords: "halal restaurants", "halal food", "muslim restaurants"
   Long-tail Keywords: "halal restaurants near me", "verified halal dining"
   Location-based: "[City] halal restaurants", "halal food [State]"
   Cuisine-specific: "halal [cuisine] restaurant"
2. Content Optimization
   Semantic HTML with proper heading structure
   Alt text for all images
   Descriptive URLs with restaurant names and locations
   Internal linking between related pages
   User-generated content (reviews, verifications)
