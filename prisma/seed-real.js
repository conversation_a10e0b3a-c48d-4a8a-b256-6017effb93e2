const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();

// Helper function to generate slug
function generateSlug(name, city, address) {
  const baseSlug = `${name}-${city}-${address}`
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/(^-|-$)/g, "");

  const randomString = Math.random().toString(36).substring(2, 8);
  return `${baseSlug}-${randomString}`;
}

// Sample restaurant data
const restaurantData = [
  {
    name: "Mama Mia's Italian Kitchen",
    address: "123 Main Street",
    city: "New York",
    state: "NY",
    zipcode: "10001",
    type: "Dine-in",
    phone: "(*************",
    website: "https://mamamias-nyc.com",
    cuisines: ["Italian", "Mediterranean"],
    highlights: [
      "Outdoor Seating",
      "Family-Friendly",
      "Wine Bar",
      "Reservations",
    ],
    lat: 40.7589,
    lng: -73.9851,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Dragon Palace",
    address: "456 Chinatown Ave",
    city: "San Francisco",
    state: "CA",
    zipcode: "94108",
    type: "Dine-in",
    phone: "(*************",
    website: "https://dragonpalace-sf.com",
    cuisines: ["Chinese", "Cantonese"],
    highlights: ["Dim Sum", "Private Dining", "Parking Available", "Takeout"],
    lat: 37.7749,
    lng: -122.4194,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Taco Libre Food Truck",
    address: "Mobile Location",
    city: "Austin",
    state: "TX",
    zipcode: "78701",
    type: "Food Truck",
    phone: "(*************",
    website: null,
    cuisines: ["Mexican", "Street Food"],
    highlights: ["Vegan Options", "Gluten-Free Options", "Quick Service"],
    lat: 30.2672,
    lng: -97.7431,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "The Burger Joint",
    address: "789 Broadway",
    city: "Chicago",
    state: "IL",
    zipcode: "60601",
    type: "Dine-in",
    phone: "(*************",
    website: "https://burgerjoint-chicago.com",
    cuisines: ["American", "Burgers"],
    highlights: ["Full Bar", "Happy Hour", "Sports Bar", "Late Night"],
    lat: 41.8781,
    lng: -87.6298,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Sakura Sushi Bar",
    address: "321 Pine Street",
    city: "Seattle",
    state: "WA",
    zipcode: "98101",
    type: "Dine-in",
    phone: "(*************",
    website: "https://sakurasushi-seattle.com",
    cuisines: ["Japanese", "Sushi"],
    highlights: ["Fresh Fish", "Sake Bar", "Chef's Special", "Reservations"],
    lat: 47.6062,
    lng: -122.3321,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Spice Route Indian Cuisine",
    address: "654 Curry Lane",
    city: "Los Angeles",
    state: "CA",
    zipcode: "90210",
    type: "Dine-in",
    phone: "(*************",
    website: "https://spiceroute-la.com",
    cuisines: ["Indian", "Vegetarian"],
    highlights: ["Vegan Options", "Spicy Food", "Buffet", "Catering"],
    lat: 34.0522,
    lng: -118.2437,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Le Petit Bistro",
    address: "987 French Quarter",
    city: "New Orleans",
    state: "LA",
    zipcode: "70116",
    type: "Dine-in",
    phone: "(*************",
    website: "https://lepetitbistro-nola.com",
    cuisines: ["French", "Creole"],
    highlights: ["Wine Selection", "Romantic", "Live Music", "Brunch"],
    lat: 29.9511,
    lng: -90.0715,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Seoul Kitchen",
    address: "147 K-Town Street",
    city: "Los Angeles",
    state: "CA",
    zipcode: "90005",
    type: "Dine-in",
    phone: "(*************",
    website: "https://seoulkitchen-la.com",
    cuisines: ["Korean", "BBQ"],
    highlights: ["Korean BBQ", "Karaoke", "Late Night", "Group Dining"],
    lat: 34.0522,
    lng: -118.2437,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Green Garden Cafe",
    address: "258 Organic Way",
    city: "Portland",
    state: "OR",
    zipcode: "97201",
    type: "Cafe",
    phone: "(*************",
    website: "https://greengarden-pdx.com",
    cuisines: ["Vegetarian", "Organic"],
    highlights: ["Vegan Options", "Organic", "Free Wi-Fi", "Pet-Friendly"],
    lat: 45.5152,
    lng: -122.6784,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "BBQ Smokehouse",
    address: "369 Smoke Street",
    city: "Kansas City",
    state: "MO",
    zipcode: "64111",
    type: "Dine-in",
    phone: "(*************",
    website: "https://bbqsmokehouse-kc.com",
    cuisines: ["American", "BBQ"],
    highlights: ["Smoked Meats", "Full Bar", "Outdoor Seating", "Catering"],
    lat: 39.0997,
    lng: -94.5786,
    isPublished: true,
    isFeatured: true,
  },
];

// Additional restaurants to reach 20+
const moreRestaurants = [
  {
    name: "Mediterranean Delight",
    address: "741 Olive Street",
    city: "Miami",
    state: "FL",
    zipcode: "33101",
    type: "Dine-in",
    phone: "(*************",
    website: "https://meddelight-miami.com",
    cuisines: ["Mediterranean", "Greek"],
    highlights: ["Healthy Options", "Seafood", "Outdoor Seating", "Wine Bar"],
    lat: 25.7617,
    lng: -80.1918,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Pizza Corner",
    address: "852 Cheese Avenue",
    city: "Boston",
    state: "MA",
    zipcode: "02101",
    type: "Takeout",
    phone: "(*************",
    website: null,
    cuisines: ["Italian", "Pizza"],
    highlights: ["Delivery", "Late Night", "Quick Service", "Family-Friendly"],
    lat: 42.3601,
    lng: -71.0589,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Thai Orchid",
    address: "963 Spice Road",
    city: "Denver",
    state: "CO",
    zipcode: "80201",
    type: "Dine-in",
    phone: "(*************",
    website: "https://thaiorchid-denver.com",
    cuisines: ["Thai", "Asian"],
    highlights: [
      "Spicy Food",
      "Vegetarian Options",
      "Lunch Specials",
      "Takeout",
    ],
    lat: 39.7392,
    lng: -104.9903,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Steakhouse Prime",
    address: "174 Beef Boulevard",
    city: "Dallas",
    state: "TX",
    zipcode: "75201",
    type: "Dine-in",
    phone: "(*************",
    website: "https://steakhouseprime-dallas.com",
    cuisines: ["American", "Steakhouse"],
    highlights: [
      "Prime Cuts",
      "Wine Selection",
      "Private Dining",
      "Valet Parking",
    ],
    lat: 32.7767,
    lng: -96.797,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Noodle House",
    address: "285 Ramen Row",
    city: "San Diego",
    state: "CA",
    zipcode: "92101",
    type: "Dine-in",
    phone: "(*************",
    website: "https://noodlehouse-sd.com",
    cuisines: ["Japanese", "Ramen"],
    highlights: [
      "Authentic Ramen",
      "Quick Service",
      "Vegetarian Broth",
      "Late Night",
    ],
    lat: 32.7157,
    lng: -117.1611,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Breakfast Club Diner",
    address: "396 Morning Street",
    city: "Phoenix",
    state: "AZ",
    zipcode: "85001",
    type: "Diner",
    phone: "(*************",
    website: "https://breakfastclub-phx.com",
    cuisines: ["American", "Breakfast"],
    highlights: ["All Day Breakfast", "Pancakes", "Coffee", "Family-Friendly"],
    lat: 33.4484,
    lng: -112.074,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Coastal Seafood Grill",
    address: "507 Harbor View",
    city: "San Diego",
    state: "CA",
    zipcode: "92102",
    type: "Dine-in",
    phone: "(*************",
    website: "https://coastalseafood-sd.com",
    cuisines: ["Seafood", "American"],
    highlights: ["Fresh Catch", "Ocean View", "Happy Hour", "Outdoor Seating"],
    lat: 32.7157,
    lng: -117.1611,
    isPublished: true,
    isFeatured: true,
  },
  {
    name: "Himalayan Spice",
    address: "618 Mountain Road",
    city: "Boulder",
    state: "CO",
    zipcode: "80301",
    type: "Dine-in",
    phone: "(*************",
    website: "https://himalayanspice-boulder.com",
    cuisines: ["Nepalese", "Indian"],
    highlights: [
      "Authentic Spices",
      "Vegetarian Options",
      "Mountain Views",
      "Lunch Buffet",
    ],
    lat: 40.015,
    lng: -105.2705,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Craft Beer & Burgers",
    address: "729 Brewery Lane",
    city: "Portland",
    state: "OR",
    zipcode: "97202",
    type: "Bar",
    phone: "(*************",
    website: "https://craftbeerburgers-pdx.com",
    cuisines: ["American", "Pub Food"],
    highlights: ["Craft Beer", "Local Brews", "Burgers", "Sports Bar"],
    lat: 45.5152,
    lng: -122.6784,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Ethiopian Flavors",
    address: "840 Spice Market",
    city: "Washington",
    state: "DC",
    zipcode: "20001",
    type: "Dine-in",
    phone: "(*************",
    website: "https://ethiopianflavors-dc.com",
    cuisines: ["Ethiopian", "African"],
    highlights: [
      "Traditional Dining",
      "Vegetarian Options",
      "Coffee Ceremony",
      "Authentic",
    ],
    lat: 38.9072,
    lng: -77.0369,
    isPublished: true,
    isFeatured: false,
  },
  {
    name: "Gelato Dreams",
    address: "951 Sweet Street",
    city: "Las Vegas",
    state: "NV",
    zipcode: "89101",
    type: "Dessert",
    phone: "(*************",
    website: "https://gelatodreams-vegas.com",
    cuisines: ["Italian", "Dessert"],
    highlights: ["Artisan Gelato", "Late Night", "Takeout", "Instagram Worthy"],
    lat: 36.1699,
    lng: -115.1398,
    isPublished: true,
    isFeatured: false,
  },
];

async function main() {
  console.log("🌱 Starting seed...");

  // Clear existing data
  console.log("🧹 Clearing existing restaurants...");
  await prisma.restaurant.deleteMany();

  // Combine all restaurant data
  const allRestaurants = [];

  console.log(`📝 Creating ${allRestaurants.length} restaurants...`);

  // Create restaurants
  for (const restaurant of allRestaurants) {
    const slug = generateSlug(
      restaurant.name,
      restaurant.city,
      restaurant.address
    );

    await prisma.restaurant.create({
      data: {
        ...restaurant,
        slug,
      },
    });

    console.log(
      `✅ Created: ${restaurant.name} in ${restaurant.city}, ${restaurant.state}`
    );
  }

  console.log("🎉 Seed completed successfully!");
  console.log(`📊 Total restaurants created: ${allRestaurants.length}`);
  console.log(
    `⭐ Featured restaurants: ${
      allRestaurants.filter((r) => r.isFeatured).length
    }`
  );
  console.log(
    `🏙️ Cities covered: ${
      [...new Set(allRestaurants.map((r) => r.city))].length
    }`
  );
}

main()
  .catch((e) => {
    console.error("❌ Seed failed:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
