-- CreateTable
CREATE TABLE "HalalVerification" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "restaurantId" TEXT NOT NULL,
    "isHalal" BOOLEAN NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HalalVerification_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "HalalVerification_userId_idx" ON "HalalVerification"("userId");

-- CreateIndex
CREATE INDEX "HalalVerification_restaurantId_idx" ON "HalalVerification"("restaurantId");

-- CreateIndex
CREATE INDEX "HalalVerification_isHalal_idx" ON "HalalVerification"("isHalal");

-- CreateIndex
CREATE UNIQUE INDEX "HalalVerification_userId_restaurantId_key" ON "HalalVerification"("userId", "restaurantId");

-- AddForeignKey
ALTER TABLE "HalalVerification" ADD CONSTRAINT "HalalVerification_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "HalalVerification" ADD CONSTRAINT "HalalVerification_restaurantId_fkey" FOREIGN KEY ("restaurantId") REFERENCES "Restaurant"("id") ON DELETE CASCADE ON UPDATE CASCADE;
